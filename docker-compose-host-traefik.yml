version: '3.8'

# Docker Compose for use with Host-based Traefik
# Similar to how you used nginx on host with container backends

services:
  # Document processing instances
  app-documents:
    image: eko-backend:latest
    ports:
      - "8000:8000"  # Expose directly since Traefik is on host
    environment:
      - PORT=8000
      - SERVICE_NAME=eko-backend-documents
    extra_hosts:
      - minio.nextai.asia:*************
    restart: unless-stopped
    deploy:
      replicas: 1
    # Optional: Keep labels for host Traefik Docker provider
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.documents.rule=Host(`eko-api2.nextai.asia`) && (PathPrefix(`/setup_files`) || PathPrefix(`/process-documents`) || PathPrefix(`/add-documents`))"
      - "traefik.http.routers.documents.entrypoints=websecure"
      - "traefik.http.routers.documents.tls=true"
      - "traefik.http.routers.documents.priority=100"
      - "traefik.http.services.app-documents.loadbalancer.server.port=8000"

  # General app for all other routes
  app:
    image: eko-backend:latest
    ports:
      - "8001-8010:8000"  # Multiple ports for scaling
    environment:
      - PORT=8000
      - SERVICE_NAME=eko-backend-general
    extra_hosts:
      - minio.nextai.asia:*************
    restart: unless-stopped
    deploy:
      replicas: 2
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.app.rule=Host(`eko-api2.nextai.asia`)"
      - "traefik.http.routers.app.entrypoints=websecure"
      - "traefik.http.routers.app.tls=true"
      - "traefik.http.routers.app.priority=1"
      - "traefik.http.services.app.loadbalancer.server.port=8000"

# No networks needed since Traefik is on host
# Containers will be accessible via localhost:port
