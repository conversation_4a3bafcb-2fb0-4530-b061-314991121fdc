#!/bin/bash

# Install Traefik on Host System
# Similar to how you used nginx configuration files

echo "=== Installing Traefik on Host System ==="

# Create traefik user
sudo useradd --system --shell /bin/false traefik

# Create directories
sudo mkdir -p /etc/traefik
sudo mkdir -p /etc/traefik/dynamic
sudo mkdir -p /var/log/traefik

# Download Traefik binary
TRAEFIK_VERSION="v2.10.4"
echo "Downloading Traefik ${TRAEFIK_VERSION}..."
wget -O /tmp/traefik.tar.gz "https://github.com/traefik/traefik/releases/download/${TRAEFIK_VERSION}/traefik_${TRAEFIK_VERSION}_linux_amd64.tar.gz"

# Extract and install
cd /tmp
tar -xzf traefik.tar.gz
sudo mv traefik /usr/local/bin/
sudo chmod +x /usr/local/bin/traefik
sudo chown root:root /usr/local/bin/traefik

# Create configuration files
echo "Creating Traefik configuration..."

# Main static configuration
sudo tee /etc/traefik/traefik.yml > /dev/null <<EOF
global:
  sendAnonymousUsage: false

api:
  dashboard: true
  insecure: true

entryPoints:
  web:
    address: ":80"
    http:
      redirections:
        entrypoint:
          to: websecure
          scheme: https
  websecure:
    address: ":443"
  http-api:
    address: ":8201"

providers:
  # File provider for static configuration
  file:
    directory: /etc/traefik/dynamic
    watch: true
  
  # Docker provider (if you still want to use Docker labels)
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    watch: true

tls:
  certificates:
    - certFile: /etc/letsencrypt/live/eko-api2.nextai.asia/fullchain.pem
      keyFile: /etc/letsencrypt/live/eko-api2.nextai.asia/privkey.pem

log:
  level: INFO
  filePath: /var/log/traefik/traefik.log

accessLog:
  filePath: /var/log/traefik/access.log

ping: {}
EOF

# Create systemd service
sudo tee /etc/systemd/system/traefik.service > /dev/null <<EOF
[Unit]
Description=Traefik
After=network-online.target
Wants=network-online.target

[Service]
Type=simple
User=traefik
Group=traefik
ExecStart=/usr/local/bin/traefik --configfile=/etc/traefik/traefik.yml
Restart=on-failure
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=traefik
KillMode=mixed
KillSignal=SIGTERM

[Install]
WantedBy=multi-user.target
EOF

# Set permissions
sudo chown -R traefik:traefik /etc/traefik
sudo chown -R traefik:traefik /var/log/traefik

# Add traefik user to docker group (if using Docker provider)
sudo usermod -aG docker traefik

echo "✅ Traefik installed successfully!"
echo "📁 Configuration directory: /etc/traefik/"
echo "📄 Main config: /etc/traefik/traefik.yml"
echo "📁 Dynamic configs: /etc/traefik/dynamic/"
echo "📋 Logs: /var/log/traefik/"

echo ""
echo "Next steps:"
echo "1. Create dynamic configuration files in /etc/traefik/dynamic/"
echo "2. Enable and start the service: sudo systemctl enable --now traefik"
echo "3. Check status: sudo systemctl status traefik"
