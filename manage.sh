#!/bin/bash

# Simple Traefik Management Script

case "$1" in
    start)
        echo "🚀 Starting Traefik services..."
        docker-compose up -d
        ;;
    scale)
        if [ -z "$2" ]; then
            echo "Usage: $0 scale <number>"
            echo "Example: $0 scale 3"
            exit 1
        fi
        echo "📈 Scaling eko-api to $2 instances..."
        docker-compose up -d --scale eko-api=$2
        ;;
    stop)
        echo "🛑 Stopping Traefik services..."
        docker-compose down
        ;;
    restart)
        echo "🔄 Restarting Traefik services..."
        docker-compose down && docker-compose up -d
        ;;
    logs)
        echo "📋 Showing logs..."
        if [ -n "$2" ]; then
            docker-compose logs -f $2
        else
            docker-compose logs -f
        fi
        ;;
    status)
        echo "📊 Service Status:"
        docker-compose ps
        echo ""
        echo "🌐 Access Points:"
        source .env 2>/dev/null || true
        echo "  - Main App: https://${DOMAIN:-localhost}:8201"
        echo "  - Dashboard: http://${DOMAIN:-localhost}:8202"
        echo "  - HTTPS: https://${DOMAIN:-localhost}"
        ;;
    build)
        echo "🔨 Building application..."
        docker build -t eko-backend:latest .
        ;;
    acme-check)
        echo "🔒 Checking ACME file..."
        if [ -f config/certs/cloudflare-acme.json ]; then
            echo "✅ ACME file exists"
            echo "📊 File size: $(stat -c%s config/certs/cloudflare-acme.json) bytes"
            if [ -s config/certs/cloudflare-acme.json ]; then
                echo "✅ ACME file contains data (certificates likely present)"
            else
                echo "⚠️  ACME file is empty (no certificates yet)"
            fi
        else
            echo "❌ ACME file missing"
            echo "🔧 Creating ACME file..."
            mkdir -p config/certs
            touch config/certs/cloudflare-acme.json
            chmod 600 config/certs/cloudflare-acme.json
            echo "✅ ACME file created"
        fi
        ;;
    *)
        echo "Usage: $0 {start|scale|stop|restart|logs|status|build|acme-check}"
        echo ""
        echo "Examples:"
        echo "  $0 start              # Start services"
        echo "  $0 scale 5            # Scale to 5 API instances"
        echo "  $0 logs traefik-api   # View Traefik logs"
        echo "  $0 acme-check         # Check ACME certificate file"
        exit 1
        ;;
esac
