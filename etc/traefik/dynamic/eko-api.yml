# Dynamic configuration for eko-api2.nextai.asia
# This file is similar to nginx conf.d/${DOMAIN_NAME}.conf

http:
  # Routers
  routers:
    # Document processing routes (high priority)
    documents-https:
      rule: "Host(`eko-api2.nextai.asia`) && (PathPrefix(`/setup_files`) || PathPrefix(`/process-documents`) || PathPrefix(`/add-documents`))"
      entryPoints:
        - websecure
      service: app-documents
      priority: 100
      middlewares:
        - ws-headers
      tls: {}

    documents-http:
      rule: "Host(`eko-api2.nextai.asia`) && (PathPrefix(`/setup_files`) || PathPrefix(`/process-documents`) || PathPrefix(`/add-documents`))"
      entryPoints:
        - http-api
      service: app-documents
      priority: 100
      middlewares:
        - ws-headers

    # General app routes (catch-all, lower priority)
    app-https:
      rule: "Host(`eko-api2.nextai.asia`)"
      entryPoints:
        - websecure
      service: app-general
      priority: 1
      tls: {}

    app-http:
      rule: "Host(`eko-api2.nextai.asia`)"
      entryPoints:
        - http-api
      service: app-general
      priority: 1

    # HTTP to HTTPS redirect
    app-redirect:
      rule: "Host(`eko-api2.nextai.asia`)"
      entryPoints:
        - web
      middlewares:
        - https-redirect

  # Services (Backend definitions)
  services:
    app-documents:
      loadBalancer:
        sticky:
          cookie:
            name: doc-session
            secure: true
            httpOnly: true
        servers:
          - url: "http://localhost:8000"  # Adjust to your backend

    app-general:
      loadBalancer:
        servers:
          - url: "http://localhost:8000"  # Primary backend
          - url: "http://localhost:8001"  # Secondary backend (if scaling)

  # Middlewares
  middlewares:
    https-redirect:
      redirectScheme:
        scheme: https

    ws-headers:
      headers:
        customRequestHeaders:
          X-Forwarded-Proto: "https"
        customResponseHeaders:
          Access-Control-Allow-Origin: "*"
          Access-Control-Allow-Methods: "GET,POST,OPTIONS,HEAD"
          Access-Control-Allow-Headers: "*"

    # CORS middleware
    cors:
      headers:
        accessControlAllowMethods:
          - GET
          - POST
          - PUT
          - DELETE
          - OPTIONS
        accessControlAllowOriginList:
          - "*"
        accessControlAllowHeaders:
          - "*"
        accessControlMaxAge: 100
        addVaryHeader: true

    # Rate limiting
    rate-limit:
      rateLimit:
        burst: 100
        average: 50

    # Security headers
    security-headers:
      headers:
        frameDeny: true
        sslRedirect: true
        browserXssFilter: true
        contentTypeNosniff: true
        forceSTSHeader: true
        stsIncludeSubdomains: true
        stsPreload: true
        stsSeconds: 31536000
