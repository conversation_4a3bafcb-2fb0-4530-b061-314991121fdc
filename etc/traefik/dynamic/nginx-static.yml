# Traefik routing to nginx for static content + FastAPI for dynamic content
# /etc/traefik/dynamic/nginx-static.yml

http:
  routers:
    # Static content to nginx
    static-content:
      rule: "Host(`eko-api2.nextai.asia`) && (PathPrefix(`/static/`) || PathPrefix(`/media/`) || PathPrefix(`/assets/`))"
      entryPoints:
        - websecure
      service: nginx-static
      priority: 200
      tls: {}

    # API documentation and admin
    api-docs:
      rule: "Host(`eko-api2.nextai.asia`) && (PathPrefix(`/docs`) || PathPrefix(`/redoc`) || PathPrefix(`/openapi.json`))"
      entryPoints:
        - websecure
      service: fastapi-backend
      priority: 150
      tls: {}

    # Document processing (high priority)
    documents-api:
      rule: "Host(`eko-api2.nextai.asia`) && (PathPrefix(`/setup_files`) || PathPrefix(`/process-documents`) || PathPrefix(`/add-documents`))"
      entryPoints:
        - websecure
      service: fastapi-backend
      priority: 100
      middlewares:
        - rate-limit-upload
        - cors-headers
      tls: {}

    # General API routes
    api-routes:
      rule: "Host(`eko-api2.nextai.asia`) && PathPrefix(`/api/`)"
      entryPoints:
        - websecure
      service: fastapi-backend
      priority: 50
      middlewares:
        - rate-limit-api
        - cors-headers
      tls: {}

    # Catch-all for FastAPI
    fastapi-catchall:
      rule: "Host(`eko-api2.nextai.asia`)"
      entryPoints:
        - websecure
      service: fastapi-backend
      priority: 1
      middlewares:
        - cors-headers
      tls: {}

  services:
    # Nginx for static content
    nginx-static:
      loadBalancer:
        servers:
          - url: "http://localhost:8080"  # nginx serving static files

    # FastAPI backend
    fastapi-backend:
      loadBalancer:
        sticky:
          cookie:
            name: "api-session"
            secure: true
            httpOnly: true
        servers:
          - url: "http://localhost:8000"
          - url: "http://localhost:8001"

  middlewares:
    # Rate limiting for uploads
    rate-limit-upload:
      rateLimit:
        burst: 5
        average: 2

    # Rate limiting for API
    rate-limit-api:
      rateLimit:
        burst: 20
        average: 10

    # CORS headers
    cors-headers:
      headers:
        accessControlAllowMethods:
          - GET
          - POST
          - PUT
          - DELETE
          - OPTIONS
        accessControlAllowOriginList:
          - "*"
        accessControlAllowHeaders:
          - "*"
        accessControlMaxAge: 100
        addVaryHeader: true

    # Security headers
    security-headers:
      headers:
        frameDeny: true
        sslRedirect: true
        browserXssFilter: true
        contentTypeNosniff: true
        forceSTSHeader: true
        stsIncludeSubdomains: true
        stsPreload: true
        stsSeconds: 31536000
