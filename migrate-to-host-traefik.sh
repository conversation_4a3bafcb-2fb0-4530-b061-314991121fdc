#!/bin/bash

# Migration script from containerized Traefik to host-based Traefik
# Similar to your nginx approach

echo "=== Migrating from Container Traefik to Host Traefik ==="

# Step 1: Backup current setup
echo "1️⃣ Backing up current configuration..."
mkdir -p backup/$(date +%Y%m%d_%H%M%S)
cp docker-compose.yml backup/$(date +%Y%m%d_%H%M%S)/
cp -r traefik/ backup/$(date +%Y%m%d_%H%M%S)/

# Step 2: Stop current containerized Traefik
echo "2️⃣ Stopping current containerized setup..."
docker-compose down

# Step 3: Install Traefik on host
echo "3️⃣ Installing Traefik on host..."
chmod +x install-traefik-host.sh
./install-traefik-host.sh

# Step 4: Copy and adapt configuration
echo "4️⃣ Migrating configuration..."

# Copy your existing traefik.yml as base
sudo cp traefik/traefik.yml /etc/traefik/traefik.yml

# Create dynamic configuration from your Docker labels
sudo cp etc/traefik/dynamic/eko-api.yml /etc/traefik/dynamic/

# Update configuration for host-based setup
sudo sed -i 's|endpoint: "unix:///var/run/docker.sock"|endpoint: "unix:///var/run/docker.sock"|' /etc/traefik/traefik.yml

# Set proper ownership
sudo chown -R traefik:traefik /etc/traefik/

# Step 5: Update Docker Compose for host Traefik
echo "5️⃣ Updating Docker Compose for host-based Traefik..."
cp docker-compose-host-traefik.yml docker-compose.yml

# Step 6: Start services
echo "6️⃣ Starting services..."

# Start Traefik on host
sudo systemctl enable --now traefik

# Wait for Traefik to start
sleep 5

# Start application containers
docker-compose up -d

# Step 7: Verify setup
echo "7️⃣ Verifying setup..."
echo "Checking Traefik status..."
sudo systemctl status traefik

echo "Checking Docker containers..."
docker-compose ps

echo "Testing endpoints..."
sleep 10

# Test health endpoint
echo "Testing health endpoint..."
curl -s http://localhost:8080/ping && echo "✅ Traefik health OK" || echo "❌ Traefik health failed"

# Test your domain (if accessible)
echo "Testing domain endpoint..."
curl -s -o /dev/null -w "%{http_code}" http://eko-api2.nextai.asia:8201/health && echo "✅ Domain endpoint OK" || echo "❌ Domain endpoint failed"

echo ""
echo "=== Migration Complete! ==="
echo ""
echo "📊 Traefik Dashboard: http://localhost:8080"
echo "🔧 Configuration: /etc/traefik/"
echo "📁 Dynamic configs: /etc/traefik/dynamic/"
echo "📋 Logs: /var/log/traefik/"
echo ""
echo "Management commands:"
echo "  sudo systemctl status traefik     # Check status"
echo "  sudo systemctl restart traefik    # Restart service"
echo "  ./manage-host-traefik.sh status   # Detailed status"
echo "  ./manage-host-traefik.sh logs     # View logs"
echo ""
echo "To add new domains:"
echo "  ./manage-host-traefik.sh add-domain new-domain.com"
