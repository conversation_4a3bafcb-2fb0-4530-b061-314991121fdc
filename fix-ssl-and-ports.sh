#!/bin/bash

# Comprehensive fix for SSL and port issues
# This script addresses both SSL certificate problems and port 8201 exposure

echo "🔧 Fixing SSL Certificate and Port 8201 Issues"
echo "=============================================="

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a port is open
check_port() {
    local port=$1
    if netstat -tuln | grep -q ":$port "; then
        echo "✅ Port $port is open"
        return 0
    else
        echo "❌ Port $port is not open"
        return 1
    fi
}

echo "📋 Current Status Check:"
echo "------------------------"

# Check current Docker services
echo "🐳 Checking Docker services..."
if docker-compose ps | grep -q "Up"; then
    echo "✅ Docker services are running"
    docker-compose ps
else
    echo "❌ Docker services are not running"
fi

# Check ports
echo ""
echo "🔌 Checking ports..."
check_port 80
check_port 443
check_port 8201
check_port 8082

echo ""
echo "🔐 SSL Certificate Status:"
echo "-------------------------"

# Check SSL certificate
if [ -f "/etc/letsencrypt/live/eko-api2.nextai.asia/fullchain.pem" ]; then
    echo "✅ Let's Encrypt certificate found"
    echo "📅 Certificate expiry:"
    openssl x509 -in /etc/letsencrypt/live/eko-api2.nextai.asia/fullchain.pem -noout -dates
else
    echo "❌ No Let's Encrypt certificate found"
    echo "🔍 Checking for other certificates..."
    
    # Check if there are any certificates in the expected location
    if [ -d "/etc/letsencrypt/live/eko-api2.nextai.asia" ]; then
        ls -la /etc/letsencrypt/live/eko-api2.nextai.asia/
    else
        echo "📁 Certificate directory doesn't exist"
    fi
fi

echo ""
echo "🛠️  Applying Fixes:"
echo "-------------------"

# Step 1: Restart Docker services with new configuration
echo "1️⃣  Restarting Docker services with updated configuration..."
docker-compose down
sleep 2
docker-compose up -d

echo "⏳ Waiting for services to start..."
sleep 10

# Step 2: Check if services are running
echo "2️⃣  Verifying services are running..."
docker-compose ps

# Step 3: Test endpoints
echo "3️⃣  Testing endpoints..."

# Test HTTPS (with self-signed cert tolerance)
echo "🔐 Testing HTTPS endpoint..."
if curl -k -s -o /dev/null -w "%{http_code}" https://eko-api2.nextai.asia/ | grep -q "200\|404\|401"; then
    echo "✅ HTTPS endpoint is accessible"
else
    echo "❌ HTTPS endpoint is not accessible"
fi

# Test HTTP on port 8201
echo "🌐 Testing HTTP endpoint on port 8201..."
if curl -s -o /dev/null -w "%{http_code}" http://eko-api2.nextai.asia:8201/ | grep -q "200\|404\|401"; then
    echo "✅ HTTP endpoint on port 8201 is accessible"
else
    echo "❌ HTTP endpoint on port 8201 is not accessible"
fi

echo ""
echo "🎯 Solutions Summary:"
echo "--------------------"

echo "For SSL Certificate Issues:"
echo "1. 🔧 Quick Fix (Testing): Use verify=False in your requests"
echo "   requests.post(url, verify=False)"
echo ""
echo "2. 🏆 Proper Fix: Get a valid SSL certificate"
echo "   Run: chmod +x setup-ssl.sh && ./setup-ssl.sh"
echo ""

echo "For Port 8201 Access:"
echo "1. ✅ Port 8201 has been configured in docker-compose.yml"
echo "2. ✅ HTTP entrypoint added to Traefik configuration"
echo "3. 🌐 You can now access: http://eko-api2.nextai.asia:8201"
echo ""

echo "🧪 Testing Your API:"
echo "-------------------"
echo "Run the test script: python3 test-endpoints.py"
echo ""

echo "📝 Next Steps:"
echo "1. Test your API endpoints using the test script"
echo "2. If using self-signed certificates, add verify=False to your requests"
echo "3. For production, set up proper SSL certificates using setup-ssl.sh"
echo "4. Monitor your services with: docker-compose logs -f"

echo ""
echo "✨ Setup complete!"
