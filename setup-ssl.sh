#!/bin/bash

# SSL Certificate Setup Script for eko-api2.nextai.asia
# This script helps set up proper SSL certificates using Let's Encrypt

echo "=== SSL Certificate Setup for eko-api2.nextai.asia ==="

# Check if certbot is installed
if ! command -v certbot &> /dev/null; then
    echo "Installing certbot..."
    sudo apt update
    sudo apt install -y certbot
fi

# Stop any running services that might use port 80/443
echo "Stopping Docker services temporarily..."
docker-compose down

# Generate SSL certificate
echo "Generating SSL certificate for eko-api2.nextai.asia..."
sudo certbot certonly --standalone \
    --email <EMAIL> \
    --agree-tos \
    --no-eff-email \
    -d eko-api2.nextai.asia

# Check if certificate was generated successfully
if [ -f "/etc/letsencrypt/live/eko-api2.nextai.asia/fullchain.pem" ]; then
    echo "✅ SSL certificate generated successfully!"
    
    # Set proper permissions
    sudo chmod -R 755 /etc/letsencrypt/live/eko-api2.nextai.asia/
    sudo chmod 644 /etc/letsencrypt/live/eko-api2.nextai.asia/*.pem
    
    echo "Starting Docker services..."
    docker-compose up -d
    
    echo "✅ Setup complete! Your domain should now have a valid SSL certificate."
    echo "🔗 Test your domain: https://eko-api2.nextai.asia"
    
else
    echo "❌ Failed to generate SSL certificate."
    echo "Please check:"
    echo "1. Domain eko-api2.nextai.asia points to this server's IP"
    echo "2. Ports 80 and 443 are open and accessible from the internet"
    echo "3. No firewall is blocking the connection"
fi

echo ""
echo "=== Certificate Auto-Renewal Setup ==="
echo "Setting up automatic certificate renewal..."

# Add cron job for auto-renewal
(crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet --deploy-hook 'docker-compose restart traefik'") | crontab -

echo "✅ Auto-renewal cron job added."
echo "Certificates will be automatically renewed and Traefik will be restarted."
