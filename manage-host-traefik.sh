#!/bin/bash

# Traefik Host Management Script
# Similar to nginx management but for Traefik
# Usage examples:
#   ./manage-host-traefik.sh install
#   ./manage-host-traefik.sh add-domain api.example.com
#   ./manage-host-traefik.sh restart

TRAEFIK_CONFIG_DIR="/etc/traefik"
DYNAMIC_CONFIG_DIR="/etc/traefik/dynamic"
LOG_DIR="/var/log/traefik"

case "$1" in
    install)
        echo "Installing Traefik on host..."
        chmod +x install-traefik-host.sh
        ./install-traefik-host.sh
        ;;
    
    start)
        echo "Starting Traefik service..."
        sudo systemctl start traefik
        sudo systemctl status traefik
        ;;
    
    stop)
        echo "Stopping Traefik service..."
        sudo systemctl stop traefik
        ;;
    
    restart)
        echo "Restarting Traefik service..."
        sudo systemctl restart traefik
        sudo systemctl status traefik
        ;;
    
    enable)
        echo "Enabling Traefik service (auto-start on boot)..."
        sudo systemctl enable traefik
        ;;
    
    status)
        echo "Traefik service status:"
        sudo systemctl status traefik
        echo ""
        echo "Recent logs:"
        sudo journalctl -u traefik -n 20 --no-pager
        ;;
    
    logs)
        echo "Traefik logs:"
        echo "=== Service logs ==="
        sudo journalctl -u traefik -f
        ;;
    
    access-logs)
        echo "Traefik access logs:"
        sudo tail -f ${LOG_DIR}/access.log
        ;;
    
    config-test)
        echo "Testing Traefik configuration..."
        sudo -u traefik /usr/local/bin/traefik --configfile=${TRAEFIK_CONFIG_DIR}/traefik.yml --dry-run
        ;;
    
    reload)
        echo "Reloading Traefik configuration..."
        # Traefik automatically reloads file provider configs
        echo "Configuration will be reloaded automatically (file provider watch enabled)"
        echo "Or restart service for full reload:"
        echo "sudo systemctl restart traefik"
        ;;
    
    add-domain)
        if [ -z "$2" ]; then
            echo "Usage: $0 add-domain <domain-name>"
            exit 1
        fi
        DOMAIN="$2"
        echo "Creating configuration for domain: $DOMAIN"
        
        # Copy template and replace domain
        sudo cp etc/traefik/dynamic/eko-api.yml ${DYNAMIC_CONFIG_DIR}/${DOMAIN}.yml
        sudo sed -i "s/eko-api2.nextai.asia/${DOMAIN}/g" ${DYNAMIC_CONFIG_DIR}/${DOMAIN}.yml
        sudo chown traefik:traefik ${DYNAMIC_CONFIG_DIR}/${DOMAIN}.yml
        
        echo "✅ Configuration created: ${DYNAMIC_CONFIG_DIR}/${DOMAIN}.yml"
        echo "Edit the file to customize routes and backends"
        ;;
    
    remove-domain)
        if [ -z "$2" ]; then
            echo "Usage: $0 remove-domain <domain-name>"
            exit 1
        fi
        DOMAIN="$2"
        CONFIG_FILE="${DYNAMIC_CONFIG_DIR}/${DOMAIN}.yml"
        
        if [ -f "$CONFIG_FILE" ]; then
            sudo rm "$CONFIG_FILE"
            echo "✅ Removed configuration for domain: $DOMAIN"
        else
            echo "❌ Configuration file not found: $CONFIG_FILE"
        fi
        ;;
    
    list-configs)
        echo "Available dynamic configurations:"
        ls -la ${DYNAMIC_CONFIG_DIR}/
        ;;
    
    dashboard)
        echo "Traefik Dashboard URLs:"
        echo "🌐 Local: http://localhost:8080"
        echo "🌐 External: http://$(hostname -I | awk '{print $1}'):8080"
        ;;
    
    health)
        echo "Checking Traefik health..."
        curl -s http://localhost:8080/ping && echo "✅ Traefik is healthy" || echo "❌ Traefik is not responding"
        ;;
    
    *)
        echo "Usage: $0 {install|start|stop|restart|enable|status|logs|access-logs|config-test|reload|add-domain|remove-domain|list-configs|dashboard|health}"
        echo ""
        echo "Examples:"
        echo "  $0 install              # Install Traefik on host"
        echo "  $0 start                # Start Traefik service"
        echo "  $0 status               # Check service status"
        echo "  $0 add-domain api.example.com  # Add new domain config"
        echo "  $0 logs                 # Follow service logs"
        echo "  $0 dashboard            # Show dashboard URLs"
        exit 1
        ;;
esac
