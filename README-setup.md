# Simple Traefik Setup for API with WebSocket Support

This configuration is based on **<PERSON>'s homelab approach** - clean, simple, and effective.

## 🚀 Quick Setup

### 1. Get Cloudflare API Token

1. Go to [Cloudflare Dashboard](https://dash.cloudflare.com/profile/api-tokens)
2. Create a new token with:
   - **Zone:Zone:Read** permissions
   - **Zone:DNS:Edit** permissions
   - Include your domain zone

### 2. Run Setup

```bash
# Make setup script executable
chmod +x setup-simple.sh

# Run setup with your domain and Cloudflare token
./setup-simple.sh your-domain.com your_cloudflare_api_token
```

### 3. Build and Start

```bash
# Build your application
./manage.sh build

# Start services
./manage.sh start

# Check status
./manage.sh status
```

## 📁 File Structure

```
echo_bot/
├── config/
│   ├── traefik.yaml          # Main Traefik config
│   ├── conf/
│   │   ├── tls.yaml          # TLS settings
│   │   └── headers.yaml      # Headers & middleware
│   └── certs/                # SSL certificates
├── docker-compose.yml        # Simple service definition
├── .env                      # Environment variables
└── manage.sh                 # Management script
```

## 🌐 Access Points

- **Main API**: `https://your-domain.com:8201`
- **HTTPS**: `https://your-domain.com`
- **Dashboard**: `http://your-domain.com:8202`

## 🔧 Management Commands

```bash
./manage.sh start      # Start services
./manage.sh stop       # Stop services
./manage.sh restart    # Restart services
./manage.sh logs       # View logs
./manage.sh status     # Check status
./manage.sh build      # Build application
```

## ✅ WebSocket Features

- **Sticky Sessions**: WebSocket connections stay on the same backend
- **Automatic SSL**: Cloudflare DNS challenge for certificates
- **Health Checks**: Automatic failover if backend fails
- **CORS Support**: Proper headers for API access

## 🔐 Security Features

- **TLS 1.2+**: Modern encryption
- **Security Headers**: HSTS, XSS protection, etc.
- **Rate Limiting**: Built-in protection
- **CORS**: Configured for API access

## 🐛 Troubleshooting

### Check logs
```bash
./manage.sh logs
```

### Verify domain DNS
```bash
nslookup your-domain.com
```

### Test WebSocket
```bash
python3 test-websocket.py --url wss://your-domain.com:8201/setup_files --token your-token
```

This setup follows Christian Lempa's philosophy: **simple, clean, and it just works!**
