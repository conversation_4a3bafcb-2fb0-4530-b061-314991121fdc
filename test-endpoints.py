#!/usr/bin/env python3
"""
Test script for both HTTP and HTTPS endpoints
"""

import requests
import json
from urllib3.exceptions import InsecureRequestWarning

# Disable SSL warnings for testing
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

# Test configuration
DOMAIN = "eko-api2.nextai.asia"
HTTPS_URL = f"https://{DOMAIN}"
HTTP_URL = f"http://{DOMAIN}:8201"

# Test headers
headers = {
    'accept': 'application/json',
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzdXBlcmFkbWluIiwidXNlcl9pZCI6IjY4MzU5NDIxYzE1ZTNiMTllYTk2Y2U0NCIsInJvbGUiOiJhZG1pbiIsInRlbmFudF9pZCI6IjY4NTI4MGFiN2ZhYmI1ZTBiNTkwOTZjZSIsImV4cCI6MTc1MDM0ODkwNn0.2pvD-Xrx6U_okMFQjP0UOHUmQhytJ1PbXw8zUg_RmBE',
    'Content-Type': 'application/json',
}

# Test data
json_data = [
    {
        'text': 'Test document content',
        'metadata': {
            'source': 'test',
            'type': 'document'
        },
    },
]

def test_endpoint(url, endpoint_name, use_ssl_verify=True):
    """Test a specific endpoint"""
    print(f"\n=== Testing {endpoint_name} ===")
    print(f"URL: {url}/add-documents")
    
    try:
        response = requests.post(
            f"{url}/add-documents", 
            headers=headers, 
            json=json_data,
            verify=use_ssl_verify,
            timeout=10
        )
        
        print(f"✅ Status Code: {response.status_code}")
        print(f"✅ Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print(f"✅ Response: {response.json()}")
        else:
            print(f"⚠️  Response Text: {response.text}")
            
    except requests.exceptions.SSLError as e:
        print(f"❌ SSL Error: {e}")
        print("💡 Try running with verify=False for self-signed certificates")
        
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Connection Error: {e}")
        print("💡 Check if the service is running and port is accessible")
        
    except requests.exceptions.Timeout as e:
        print(f"❌ Timeout Error: {e}")
        
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")

def main():
    print("🚀 Testing API endpoints...")
    print(f"Domain: {DOMAIN}")
    
    # Test HTTPS with SSL verification
    test_endpoint(HTTPS_URL, "HTTPS (with SSL verification)", use_ssl_verify=True)
    
    # Test HTTPS without SSL verification (for self-signed certs)
    test_endpoint(HTTPS_URL, "HTTPS (without SSL verification)", use_ssl_verify=False)
    
    # Test HTTP on port 8201
    test_endpoint(HTTP_URL, "HTTP (port 8201)", use_ssl_verify=True)
    
    print("\n" + "="*50)
    print("📋 Summary:")
    print("- If HTTPS with SSL verification works: You have a valid SSL certificate")
    print("- If HTTPS without SSL verification works: You have a self-signed certificate")
    print("- If HTTP (port 8201) works: Your HTTP endpoint is accessible")
    print("- If none work: Check if services are running and ports are open")

if __name__ == "__main__":
    main()
