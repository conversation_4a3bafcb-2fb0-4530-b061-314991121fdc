#!/bin/bash

# Traefik WebSocket Setup Script
# Configures Traefik with your domain and SSL certificates

set -e

echo "=== Traefik WebSocket Configuration Setup ==="

# Check if domain is provided
if [ -z "$1" ]; then
    echo "Usage: $0 <your-domain.com>"
    echo "Example: $0 eko-api2.nextai.asia"
    exit 1
fi

DOMAIN="$1"
echo "🌐 Setting up Traefik for domain: $DOMAIN"

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p traefik/dynamic
mkdir -p logs/traefik
mkdir -p certs

# Create ACME file for Let's Encrypt
echo "🔐 Setting up ACME storage..."
touch traefik/acme.json
chmod 600 traefik/acme.json

# Check if SSL certificates exist
SSL_PATH="/etc/letsencrypt/live/$DOMAIN"
if [ -d "$SSL_PATH" ]; then
    echo "✅ Found existing SSL certificates at $SSL_PATH"
    echo "📋 Certificate files:"
    ls -la "$SSL_PATH/"
else
    echo "⚠️  No existing SSL certificates found at $SSL_PATH"
    echo "🔧 Traefik will automatically generate Let's Encrypt certificates"
    
    # Prompt for email
    read -p "📧 Enter email for Let's Encrypt registration: " EMAIL
    if [ -n "$EMAIL" ]; then
        # Update traefik.yml with email
        sed -i "s/admin@\${DOMAIN}/$EMAIL/g" traefik/traefik.yml
        echo "✅ Email configured: $EMAIL"
    fi
fi

# Create .env file
echo "⚙️  Creating environment configuration..."
cat > .env << EOF
# Domain Configuration
DOMAIN=$DOMAIN

# Traefik Configuration
TRAEFIK_LOG_LEVEL=INFO
TRAEFIK_API_DASHBOARD=true

# SSL Configuration
SSL_PATH=$SSL_PATH

# Application Configuration
APP_REPLICAS=2
DOCUMENTS_REPLICAS=1

# Ports
HTTP_PORT=80
HTTPS_PORT=443
API_PORT=8201
DASHBOARD_PORT=8202
EOF

echo "✅ Environment file created: .env"

# Update configuration files with domain
echo "🔧 Updating configuration files..."

# Replace domain placeholders in all config files
find traefik/ -name "*.yml" -exec sed -i "s/\${DOMAIN}/$DOMAIN/g" {} \;
sed -i "s/\${DOMAIN:-localhost}/$DOMAIN/g" docker-compose.yml

echo "✅ Configuration files updated"

# Validate configuration
echo "🔍 Validating Traefik configuration..."
if command -v docker &> /dev/null; then
    docker run --rm -v "$(pwd)/traefik/traefik.yml:/etc/traefik/traefik.yml:ro" traefik:v3.0 traefik --configfile=/etc/traefik/traefik.yml --dry-run
    if [ $? -eq 0 ]; then
        echo "✅ Traefik configuration is valid"
    else
        echo "❌ Traefik configuration validation failed"
        exit 1
    fi
else
    echo "⚠️  Docker not found, skipping configuration validation"
fi

# Create management script
echo "🛠️  Creating management script..."
cat > manage-traefik.sh << 'EOF'
#!/bin/bash

# Traefik Management Script

case "$1" in
    start)
        echo "🚀 Starting Traefik services..."
        docker-compose up -d
        ;;
    stop)
        echo "🛑 Stopping Traefik services..."
        docker-compose down
        ;;
    restart)
        echo "🔄 Restarting Traefik services..."
        docker-compose down
        docker-compose up -d
        ;;
    logs)
        echo "📋 Showing Traefik logs..."
        docker-compose logs -f traefik
        ;;
    status)
        echo "📊 Service Status:"
        docker-compose ps
        echo ""
        echo "🌐 Access Points:"
        echo "  - Main App: https://$DOMAIN:8201"
        echo "  - Dashboard: http://$DOMAIN:8202"
        echo "  - HTTPS: https://$DOMAIN"
        ;;
    test-websocket)
        echo "🔌 Testing WebSocket connection..."
        curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" \
             -H "Sec-WebSocket-Key: test" -H "Sec-WebSocket-Version: 13" \
             "http://$DOMAIN:8201/setup_files"
        ;;
    dashboard)
        echo "🎛️  Opening Traefik dashboard..."
        if command -v xdg-open &> /dev/null; then
            xdg-open "http://$DOMAIN:8202"
        elif command -v open &> /dev/null; then
            open "http://$DOMAIN:8202"
        else
            echo "Dashboard URL: http://$DOMAIN:8202"
        fi
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|logs|status|test-websocket|dashboard}"
        exit 1
        ;;
esac
EOF

chmod +x manage-traefik.sh

echo ""
echo "🎉 Traefik WebSocket setup completed!"
echo ""
echo "📋 Summary:"
echo "  ✅ Domain: $DOMAIN"
echo "  ✅ SSL: Let's Encrypt (auto-generated)"
echo "  ✅ WebSocket: Enabled with sticky sessions"
echo "  ✅ Ports: 80, 443, 8201, 8202"
echo ""
echo "🚀 Next steps:"
echo "  1. Build your application: docker build -t eko-backend:latest ."
echo "  2. Start services: ./manage-traefik.sh start"
echo "  3. Check status: ./manage-traefik.sh status"
echo "  4. View dashboard: ./manage-traefik.sh dashboard"
echo "  5. Test WebSocket: ./manage-traefik.sh test-websocket"
echo ""
echo "🌐 Access URLs:"
echo "  - Main App: https://$DOMAIN:8201"
echo "  - HTTPS: https://$DOMAIN"
echo "  - Dashboard: http://$DOMAIN:8202"
EOF
