#!/usr/bin/env python3
"""
WebSocket Connection Test Script
Tests WebSocket connectivity through Traefik with sticky sessions
"""

import asyncio
import websockets
import json
import sys
import argparse
from urllib.parse import urlparse

async def test_websocket_connection(url, token=None, duration=10):
    """Test WebSocket connection with optional token authentication"""
    
    print(f"🔌 Testing WebSocket connection to: {url}")
    
    # Add token as query parameter if provided
    if token:
        separator = "&" if "?" in url else "?"
        url = f"{url}{separator}token={token}"
        print(f"🔑 Using token: {token[:10]}...")
    
    try:
        # Connect to WebSocket
        print("📡 Connecting...")
        async with websockets.connect(url) as websocket:
            print("✅ WebSocket connection established!")
            
            # Send initial message
            test_message = {
                "type": "test",
                "message": "Hello from test client",
                "timestamp": asyncio.get_event_loop().time()
            }
            
            await websocket.send(json.dumps(test_message))
            print(f"📤 Sent: {test_message}")
            
            # Listen for messages for specified duration
            print(f"👂 Listening for {duration} seconds...")
            
            try:
                # Set timeout for receiving messages
                response = await asyncio.wait_for(
                    websocket.recv(), 
                    timeout=duration
                )
                print(f"📥 Received: {response}")
                
                # Try to parse as JSON
                try:
                    data = json.loads(response)
                    print(f"📋 Parsed data: {json.dumps(data, indent=2)}")
                except json.JSONDecodeError:
                    print(f"📄 Raw response: {response}")
                    
            except asyncio.TimeoutError:
                print(f"⏰ No response received within {duration} seconds")
            
            # Keep connection alive and test persistence
            print("🔄 Testing connection persistence...")
            for i in range(3):
                ping_message = {
                    "type": "ping",
                    "sequence": i + 1,
                    "timestamp": asyncio.get_event_loop().time()
                }
                await websocket.send(json.dumps(ping_message))
                print(f"📤 Ping {i + 1}: {ping_message}")
                
                try:
                    pong = await asyncio.wait_for(websocket.recv(), timeout=5)
                    print(f"📥 Pong {i + 1}: {pong}")
                except asyncio.TimeoutError:
                    print(f"⏰ Ping {i + 1} timeout")
                
                await asyncio.sleep(2)
            
            print("✅ WebSocket test completed successfully!")
            
    except websockets.exceptions.ConnectionClosed as e:
        print(f"❌ WebSocket connection closed: {e}")
        return False
    except websockets.exceptions.InvalidURI as e:
        print(f"❌ Invalid WebSocket URI: {e}")
        return False
    except websockets.exceptions.InvalidHandshake as e:
        print(f"❌ WebSocket handshake failed: {e}")
        return False
    except Exception as e:
        print(f"❌ WebSocket connection failed: {e}")
        return False
    
    return True

async def test_multiple_connections(base_url, token=None, connections=3):
    """Test multiple WebSocket connections to verify sticky sessions"""
    
    print(f"🔗 Testing {connections} simultaneous WebSocket connections...")
    
    tasks = []
    for i in range(connections):
        url = base_url
        if token:
            separator = "&" if "?" in url else "?"
            url = f"{url}{separator}token={token}&client_id={i}"
        
        task = asyncio.create_task(
            test_websocket_connection(url, duration=5)
        )
        tasks.append(task)
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    successful = sum(1 for result in results if result is True)
    print(f"📊 Results: {successful}/{connections} connections successful")
    
    return successful == connections

def main():
    parser = argparse.ArgumentParser(description="Test WebSocket connections through Traefik")
    parser.add_argument("--url", default="ws://localhost:8201/setup_files", 
                       help="WebSocket URL to test")
    parser.add_argument("--token", help="Authentication token")
    parser.add_argument("--duration", type=int, default=10, 
                       help="Test duration in seconds")
    parser.add_argument("--multiple", type=int, default=1, 
                       help="Number of simultaneous connections to test")
    parser.add_argument("--ssl", action="store_true", 
                       help="Use WSS (secure WebSocket)")
    
    args = parser.parse_args()
    
    # Convert to WSS if SSL flag is set
    if args.ssl:
        args.url = args.url.replace("ws://", "wss://")
    
    print("🧪 WebSocket Connection Test")
    print("=" * 50)
    print(f"URL: {args.url}")
    print(f"Token: {'Yes' if args.token else 'No'}")
    print(f"Duration: {args.duration}s")
    print(f"Connections: {args.multiple}")
    print("=" * 50)
    
    try:
        if args.multiple > 1:
            success = asyncio.run(
                test_multiple_connections(args.url, args.token, args.multiple)
            )
        else:
            success = asyncio.run(
                test_websocket_connection(args.url, args.token, args.duration)
            )
        
        if success:
            print("\n🎉 All WebSocket tests passed!")
            sys.exit(0)
        else:
            print("\n❌ WebSocket tests failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
