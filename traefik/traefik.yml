# Optimized Traefik Configuration for WebSocket Support
# Domain: ${DOMAIN} (set via environment variable)
# SSL: Let's Encrypt certificates from /etc/letsencrypt/live/${DOMAIN}/

global:
  sendAnonymousUsage: false

# API and Dashboard Configuration
api:
  dashboard: true
  insecure: true  # Dashboard accessible without auth on port 8202

# Entry Points Configuration
entryPoints:
  # HTTP entry point (redirects to HTTPS)
  web:
    address: ":80"
    http:
      redirections:
        entrypoint:
          to: websecure
          scheme: https
          permanent: true

  # HTTPS entry point (main traffic)
  websecure:
    address: ":443"
    http:
      # WebSocket specific settings
      middlewares:
        - default-headers@file
      tls:
        options: default

  # HTTP API entry point (port 8201)
  http-api:
    address: ":8201"
    http:
      middlewares:
        - default-headers@file

  # Traefik Dashboard (port 8202)
  traefik:
    address: ":8202"

# Providers Configuration
providers:
  # File provider for dynamic configuration
  file:
    directory: /etc/traefik/dynamic
    watch: true

  # Docker provider for container discovery
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    watch: true
    network: "web"

# TLS Configuration
tls:
  options:
    default:
      minVersion: "VersionTLS12"
      maxVersion: "VersionTLS13"
      sslStrategies:
        - "tls.SniStrict"
      cipherSuites:
        - "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
        - "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256"
        - "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"
        - "TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA"
      curvePreferences:
        - "CurveP521"
        - "CurveP384"
      clientAuth:
        caFiles: []
        clientAuthType: "NoClientCert"

# Certificate Configuration (Let's Encrypt)
certificatesResolvers:
  letsencrypt:
    acme:
      email: admin@${DOMAIN}
      storage: /etc/traefik/acme.json
      httpChallenge:
        entryPoint: web
      # Uncomment for staging/testing
      # caServer: https://acme-staging-v02.api.letsencrypt.org/directory

# Logging Configuration
log:
  level: INFO
  filePath: /var/log/traefik/traefik.log
  format: json

# Access Logging
accessLog:
  filePath: /var/log/traefik/access.log
  format: json
  bufferingSize: 100
  filters:
    statusCodes:
      - "400-499"
      - "500-599"
  fields:
    defaultMode: keep
    names:
      ClientUsername: drop
    headers:
      defaultMode: keep
      names:
        User-Agent: keep
        Authorization: drop
        Content-Type: keep
        X-Forwarded-For: keep

# Metrics (optional - for monitoring)
metrics:
  prometheus:
    addEntryPointsLabels: true
    addServicesLabels: true
    addRoutersLabels: true
    buckets:
      - 0.1
      - 0.3
      - 1.2
      - 5.0

# Health Check
ping:
  entryPoint: "traefik"

# Tracing (optional - for debugging)
tracing:
  serviceName: traefik
  spanNameLimit: 250
