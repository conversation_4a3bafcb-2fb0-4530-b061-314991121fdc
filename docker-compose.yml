---
# Christian Lempa Style Traefik Configuration
# Clean, simple, and effective for API with WebSocket support

networks:
  frontend:
    external: true
  backend:
    external: true

services:
  # Traefik Reverse Proxy
  traefik:
    container_name: traefik-api
    image: traefik:3.0.1
    restart: unless-stopped
    ports:
      - "80:80"       # HTTP
      - "443:443"     # HTTPS
      - "8201:8201"   # API Port
      - "8202:8080"   # Dashboard
    volumes:
      # Docker socket for service discovery
      - /var/run/docker.sock:/var/run/docker.sock:ro

      # Traefik configuration (Christian Lempa style)
      - ./config/traefik.yaml:/etc/traefik/traefik.yaml:ro
      - ./config/conf/:/etc/traefik/conf/
      - ./config/certs/:/etc/traefik/certs/

      # Logs
      - ./logs:/var/log/traefik
    environment:
      - DOMAIN=${DOMAIN:-localhost}
    networks:
      - frontend
      - backend

  # Your API Application (WebSocket enabled)
  eko-api:
    container_name: eko-backend
    image: eko-backend:latest
    restart: unless-stopped
    environment:
      - PORT=8000
      - SERVICE_NAME=eko-backend
    extra_hosts:
      - "minio.nextai.asia:*************"
    networks:
      - backend
    labels:
      - "traefik.enable=true"

      # Main API routes
      - "traefik.http.routers.api.rule=Host(`${DOMAIN:-localhost}`)"
      - "traefik.http.routers.api.entrypoints=websecure"
      - "traefik.http.routers.api.tls.certresolver=letsencrypt"
      - "traefik.http.routers.api.middlewares=default-headers@file"

      # HTTP API on port 8201
      - "traefik.http.routers.api-http.rule=Host(`${DOMAIN:-localhost}`)"
      - "traefik.http.routers.api-http.entrypoints=http-api"
      - "traefik.http.routers.api-http.middlewares=default-headers@file"

      # Service configuration
      - "traefik.http.services.eko-api.loadbalancer.server.port=8000"
      - "traefik.http.services.eko-api.loadbalancer.healthcheck.path=/health"

      # WebSocket sticky sessions for /setup_files
      - "traefik.http.services.eko-api.loadbalancer.sticky.cookie=true"
      - "traefik.http.services.eko-api.loadbalancer.sticky.cookie.name=eko-session"


