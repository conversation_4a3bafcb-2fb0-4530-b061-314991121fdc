---
# Simple Traefik Configuration for API with WebSocket support
# Uses Cloudflare DNS for HTTPS certificates

services:
  traefik:
    container_name: traefik-api
    image: traefik:3.0.1
    restart: unless-stopped
    ports:
      - "80:80"       # HTTP
      - "443:443"     # HTTPS
      - "8201:8201"   # API Port
      - "8202:8080"   # Dashboard
    volumes:
      # Docker socket for service discovery
      - /var/run/docker.sock:/var/run/docker.sock:ro

      # Traefik configuration
      - ./config/traefik.yaml:/etc/traefik/traefik.yaml:ro
      - ./config/conf/:/etc/traefik/conf/
      - ./config/certs/:/etc/traefik/certs/
    environment:
      - CF_DNS_API_TOKEN=${CF_DNS_API_TOKEN}
      - DOMAIN=${DOMAIN:-localhost}

  # Your API Application (WebSocket enabled)
  eko-api:
    container_name: eko-backend
    image: eko-backend:latest
    restart: unless-stopped
    environment:
      - PORT=8000
      - SERVICE_NAME=eko-backend
    extra_hosts:
      - "minio.nextai.asia:*************"
    labels:
      - "traefik.enable=true"

      # Main API routes (HTTPS)
      - "traefik.http.routers.api.rule=Host(`${DOMAIN:-localhost}`)"
      - "traefik.http.routers.api.entrypoints=websecure"
      - "traefik.http.routers.api.tls.certresolver=cloudflare"
      - "traefik.http.routers.api.middlewares=default-headers@file"

      # HTTP API on port 8201
      - "traefik.http.routers.api-http.rule=Host(`${DOMAIN:-localhost}`)"
      - "traefik.http.routers.api-http.entrypoints=http-api"
      - "traefik.http.routers.api-http.middlewares=default-headers@file"

      # Service configuration with WebSocket sticky sessions
      - "traefik.http.services.eko-api.loadbalancer.server.port=8000"
      - "traefik.http.services.eko-api.loadbalancer.healthcheck.path=/health"
      - "traefik.http.services.eko-api.loadbalancer.sticky.cookie=true"
      - "traefik.http.services.eko-api.loadbalancer.sticky.cookie.name=eko-session"


