version: '3.8'

# WebSocket Optimized Docker Compose Configuration
# Designed for stable WebSocket connections with sticky sessions

services:
  # Traefik Reverse Proxy
  traefik:
    image: traefik:v3.0
    container_name: traefik
    restart: unless-stopped
    command:
      - "--configFile=/etc/traefik/traefik.yml"
    ports:
      - "80:80"       # HTTP
      - "443:443"     # HTTPS
      - "8201:8201"   # HTTP API
      - "8202:8202"   # Traefik Dashboard
    volumes:
      # Traefik configuration
      - ./traefik/traefik.yml:/etc/traefik/traefik.yml:ro
      - ./traefik/dynamic:/etc/traefik/dynamic:ro
      
      # Docker socket for service discovery
      - /var/run/docker.sock:/var/run/docker.sock:ro
      
      # SSL certificates (adjust path to your domain)
      - /etc/letsencrypt:/etc/letsencrypt:ro
      
      # ACME storage for Let's Encrypt
      - ./traefik/acme.json:/etc/traefik/acme.json
      
      # Logs
      - ./logs/traefik:/var/log/traefik
    environment:
      - DOMAIN=${DOMAIN:-localhost}
    networks:
      - web
    labels:
      - "traefik.enable=true"
      # Dashboard
      - "traefik.http.routers.dashboard.rule=Host(`${DOMAIN:-localhost}`) && (PathPrefix(`/api`) || PathPrefix(`/dashboard`))"
      - "traefik.http.routers.dashboard.entrypoints=traefik"
      - "traefik.http.routers.dashboard.service=api@internal"

  # Document Processing Service (WebSocket enabled)
  app-documents:
    image: eko-backend:latest
    restart: unless-stopped
    environment:
      - PORT=8000
      - SERVICE_NAME=eko-backend-documents
      - WEBSOCKET_ENABLED=true
    extra_hosts:
      - "minio.nextai.asia:*************"
    networks:
      - web
    deploy:
      replicas: 1  # Single instance for WebSocket sticky sessions
    labels:
      - "traefik.enable=true"
      
      # WebSocket routes (highest priority)
      - "traefik.http.routers.websocket.rule=Host(`${DOMAIN:-localhost}`) && (Path(`/setup_files`) || PathPrefix(`/ws/`) || HeadersRegexp(`Connection`, `(?i)upgrade`))"
      - "traefik.http.routers.websocket.entrypoints=websecure,http-api"
      - "traefik.http.routers.websocket.priority=1000"
      - "traefik.http.routers.websocket.middlewares=websocket-headers@file,websocket-sticky@file"
      - "traefik.http.routers.websocket.tls.certresolver=letsencrypt"
      
      # Document processing routes
      - "traefik.http.routers.documents.rule=Host(`${DOMAIN:-localhost}`) && (PathPrefix(`/process-documents`) || PathPrefix(`/add-documents`) || PathPrefix(`/check_status`))"
      - "traefik.http.routers.documents.entrypoints=websecure,http-api"
      - "traefik.http.routers.documents.priority=900"
      - "traefik.http.routers.documents.middlewares=default-headers@file,documents-sticky@file,rate-limit-upload@file"
      - "traefik.http.routers.documents.tls.certresolver=letsencrypt"
      
      # Service configuration with sticky sessions
      - "traefik.http.services.app-documents.loadbalancer.server.port=8000"
      - "traefik.http.services.app-documents.loadbalancer.sticky.cookie=true"
      - "traefik.http.services.app-documents.loadbalancer.sticky.cookie.name=websocket-session"
      - "traefik.http.services.app-documents.loadbalancer.sticky.cookie.secure=true"
      - "traefik.http.services.app-documents.loadbalancer.sticky.cookie.httponly=true"
      - "traefik.http.services.app-documents.loadbalancer.sticky.cookie.samesite=lax"
      
      # Health check
      - "traefik.http.services.app-documents.loadbalancer.healthcheck.path=/health"
      - "traefik.http.services.app-documents.loadbalancer.healthcheck.interval=10s"
      - "traefik.http.services.app-documents.loadbalancer.healthcheck.timeout=5s"

  # General Application Service
  app:
    image: eko-backend:latest
    restart: unless-stopped
    environment:
      - PORT=8000
      - SERVICE_NAME=eko-backend-general
    extra_hosts:
      - "minio.nextai.asia:*************"
    networks:
      - web
    deploy:
      replicas: 2  # Multiple instances for load balancing
    labels:
      - "traefik.enable=true"
      
      # API routes
      - "traefik.http.routers.api.rule=Host(`${DOMAIN:-localhost}`) && PathPrefix(`/api/`)"
      - "traefik.http.routers.api.entrypoints=websecure,http-api"
      - "traefik.http.routers.api.priority=800"
      - "traefik.http.routers.api.middlewares=default-headers@file,rate-limit-api@file"
      - "traefik.http.routers.api.tls.certresolver=letsencrypt"
      
      # Health check route
      - "traefik.http.routers.health.rule=Host(`${DOMAIN:-localhost}`) && Path(`/health`)"
      - "traefik.http.routers.health.entrypoints=websecure,http-api"
      - "traefik.http.routers.health.priority=950"
      - "traefik.http.routers.health.middlewares=default-headers@file"
      - "traefik.http.routers.health.tls.certresolver=letsencrypt"
      
      # Catch-all routes
      - "traefik.http.routers.catchall.rule=Host(`${DOMAIN:-localhost}`)"
      - "traefik.http.routers.catchall.entrypoints=websecure,http-api"
      - "traefik.http.routers.catchall.priority=1"
      - "traefik.http.routers.catchall.middlewares=default-headers@file,rate-limit-general@file"
      - "traefik.http.routers.catchall.tls.certresolver=letsencrypt"
      
      # Service configuration
      - "traefik.http.services.app.loadbalancer.server.port=8000"
      - "traefik.http.services.app.loadbalancer.healthcheck.path=/health"
      - "traefik.http.services.app.loadbalancer.healthcheck.interval=10s"
      - "traefik.http.services.app.loadbalancer.healthcheck.timeout=5s"

networks:
  web:
    driver: bridge
    name: web

# Volumes for persistent data
volumes:
  traefik-acme:
    driver: local
