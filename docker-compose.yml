version: '3.8'

services:
  traefik:
    image: traefik:v2.10
    command:
      - "--configFile=/etc/traefik/traefik.yml"
    ports:
      - "80:80"
      - "443:443"
      - "8082:8080"  # Dashboard
      - "8201:8201"  # HTTP access port
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik/traefik.yml:/etc/traefik/traefik.yml:ro
      - /etc/letsencrypt/live/eko-api2.nextai.asia:/etc/letsencrypt/live/eko-api2.nextai.asia:ro
    networks:
      - web
    restart: unless-stopped

  # Document processing instances with token-based routing
  app-documents:
    image: eko-backend:latest
    networks:
      - web
    environment:
      - PORT=8000
      - SERVICE_NAME=eko-backend-documents
    extra_hosts:
      - minio.nextai.asia:*************
    restart: unless-stopped
    deploy:
      replicas: 1
    labels:
      - "traefik.enable=true"
      # Document processing routes with higher priority (HTTPS)
      - "traefik.http.routers.documents.rule=Host(`${DOMAIN}`) && (PathPrefix(`/setup_files`) || PathPrefix(`/process-documents`) || PathPrefix(`/add-documents`))"
      - "traefik.http.routers.documents.entrypoints=websecure"
      - "traefik.http.routers.documents.tls=true"
      - "traefik.http.routers.documents.priority=100"
      - "traefik.http.routers.documents.middlewares=ws-headers"
      - "traefik.http.routers.documents.service=app-documents"
      # Document processing routes for HTTP on port 8201
      - "traefik.http.routers.documents-http.rule=Host(`${DOMAIN}`) && (PathPrefix(`/setup_files`) || PathPrefix(`/process-documents`) || PathPrefix(`/add-documents`))"
      - "traefik.http.routers.documents-http.entrypoints=http-api"
      - "traefik.http.routers.documents-http.priority=100"
      - "traefik.http.routers.documents-http.middlewares=ws-headers"
      - "traefik.http.routers.documents-http.service=app-documents"
      # Token-based session affinity for document processing
      - "traefik.http.services.app-documents.loadbalancer.server.port=8000"
      - "traefik.http.services.app-documents.loadbalancer.sticky.cookie=true"
      - "traefik.http.services.app-documents.loadbalancer.sticky.cookie.name=doc-session"
      - "traefik.http.services.app-documents.loadbalancer.sticky.cookie.secure=true"
      - "traefik.http.services.app-documents.loadbalancer.sticky.cookie.httpOnly=true"

  # General app for all other routes
  app:
    image: eko-backend:latest
    networks:
      - web
    environment:
      - PORT=8000
      - SERVICE_NAME=eko-backend-general
    extra_hosts:
      - minio.nextai.asia:*************
    restart: unless-stopped
    deploy:
      replicas: 2
    labels:
      - "traefik.enable=true"
      # General routes (catch-all with lower priority) - HTTPS
      - "traefik.http.routers.app.rule=Host(`${DOMAIN}`)"
      - "traefik.http.routers.app.entrypoints=websecure"
      - "traefik.http.routers.app.tls=true"
      - "traefik.http.routers.app.priority=1"
      - "traefik.http.services.app.loadbalancer.server.port=8000"
      # General routes for HTTP on port 8201
      - "traefik.http.routers.app-http.rule=Host(`${DOMAIN}`)"
      - "traefik.http.routers.app-http.entrypoints=http-api"
      - "traefik.http.routers.app-http.priority=1"
      - "traefik.http.routers.app-http.service=app"
      # HTTP to HTTPS redirect
      - "traefik.http.routers.app-redirect.rule=Host(`${DOMAIN}`)"
      - "traefik.http.routers.app-redirect.entrypoints=web"
      - "traefik.http.routers.app-redirect.middlewares=https-redirect"
      # Middlewares
      - "traefik.http.middlewares.https-redirect.redirectscheme.scheme=https"
      - "traefik.http.middlewares.ws-headers.headers.customrequestheaders.X-Forwarded-Proto=https"
      - "traefik.http.middlewares.ws-headers.headers.customresponseheaders.Access-Control-Allow-Origin=*"
      - "traefik.http.middlewares.ws-headers.headers.customresponseheaders.Access-Control-Allow-Methods=GET,POST,OPTIONS,HEAD"
      - "traefik.http.middlewares.ws-headers.headers.customresponseheaders.Access-Control-Allow-Headers=*"

networks:
  web:
    driver: bridge

# Scaling Profiles - Use with: docker-compose --profile <profile-name> up -d
# LOCAL (default): 3 total (1 document + 2 general)
# SMALL: 5 total (2 document + 3 general)
# MEDIUM: 8 total (3 document + 5 general)
# HIGH: 12 total (4 document + 8 general)
# MAX: 15 total (5 document + 10 general)

# Usage examples:
# docker-compose up -d                                    # LOCAL (default)
# docker-compose --profile small up -d                    # SMALL
# docker-compose --profile medium up -d                   # MEDIUM
# docker-compose --profile high up -d                     # HIGH
# docker-compose --profile max up -d                      # MAX

# Or use manual scaling:
# docker-compose up -d --scale app-documents=3 --scale app=5
