---
# Simple Traefik Configuration for API with WebSocket support
# Uses Cloudflare DNS for HTTPS certificates

services:
  traefik:
    container_name: traefik-api
    image: traefik:3.0.1
    restart: unless-stopped
    ports:
      - "80:80"       # HTTP
      - "443:443"     # HTTPS
      - "8201:8201"   # API Port
      - "8202:8080"   # Dashboard
    volumes:
      # Docker socket for service discovery
      - /var/run/docker.sock:/var/run/docker.sock:ro

      # Traefik configuration
      - ./config/traefik.yaml:/tmp/traefik.yaml:ro
      - ./config/conf/:/etc/traefik/conf/
      - ./config/certs/:/etc/traefik/certs/

      # Persistent ACME storage (important for certificate persistence)
      # Note: ACME file is created inside container and persisted via certs/ directory mount
      # - ./config/certs/cloudflare-acme.json:/etc/traefik/certs/cloudflare-acme.json
    environment:
      - CF_DNS_API_TOKEN=${CF_DNS_API_TOKEN}
      - CF_EMAIL=${CF_EMAIL}
      - DOMAIN=${DOMAIN:-localhost}
    # Custom startup command with environment variable substitution
    command:
      - /bin/sh
      - -c
      - |
        echo "🔐 CF_DNS_API_TOKEN: $${CF_DNS_API_TOKEN:0:10}..."
        echo "📧 CF_EMAIL: $$CF_EMAIL"
        echo "🌐 DOMAIN: $$DOMAIN"
        echo "🔧 Copying and updating traefik.yaml with email..."
        cp /tmp/traefik.yaml /etc/traefik/traefik.yaml
        sed -i "s/\$${CF_EMAIL}/$$CF_EMAIL/g" /etc/traefik/traefik.yaml
        echo "🔒 Checking ACME file..."
        if [ ! -f /etc/traefik/certs/cloudflare-acme.json ]; then
          echo "⚠️  ACME file not found - using existing or will be created by Traefik"
          # Commented out: Don't create new ACME file to avoid rate limits
          # touch /etc/traefik/certs/cloudflare-acme.json
          # chmod 600 /etc/traefik/certs/cloudflare-acme.json
        else
          echo "✅ ACME file already exists - preserving certificates"
          echo "📊 File size: $(stat -c%s /etc/traefik/certs/cloudflare-acme.json) bytes"
        fi
        echo "✅ Configuration updated"
        traefik --configfile=/etc/traefik/traefik.yaml

    # Alternative: Use default Traefik command (uncomment to use)
    # command:
    #   - "--configfile=/etc/traefik/traefik.yaml"

  # Your API Application (WebSocket enabled)
  eko-api:
    image: eko-backend:latest
    restart: unless-stopped
    environment:
      - PORT=8000
      - SERVICE_NAME=eko-backend
    extra_hosts:
      - "minio.nextai.asia:*************"
    labels:
      - "traefik.enable=true"

      # Main API routes (HTTPS)
      - "traefik.http.routers.api.rule=Host(`${DOMAIN:-localhost}`)"
      - "traefik.http.routers.api.entrypoints=websecure"
      # Commented out: Certificate resolver to avoid rate limits
      # - "traefik.http.routers.api.tls.certresolver=cloudflare"
      - "traefik.http.routers.api.tls=true"
      - "traefik.http.routers.api.middlewares=default-headers@file"

      # HTTP API on port 8201
      - "traefik.http.routers.api-http.rule=Host(`${DOMAIN:-localhost}`)"
      - "traefik.http.routers.api-http.entrypoints=http-api"
      - "traefik.http.routers.api-http.middlewares=default-headers@file"

      # Service configuration with WebSocket sticky sessions
      - "traefik.http.services.eko-api.loadbalancer.server.port=8000"
      - "traefik.http.services.eko-api.loadbalancer.healthcheck.path=/health"
      - "traefik.http.services.eko-api.loadbalancer.sticky.cookie=true"
      - "traefik.http.services.eko-api.loadbalancer.sticky.cookie.name=eko-session"


