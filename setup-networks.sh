#!/bin/bash

# Setup Docker Networks (Christian Lempa Style)
# Creates external networks for clean separation

echo "🌐 Setting up Docker networks..."

# Create frontend network (for Traefik and external access)
if ! docker network ls | grep -q "frontend"; then
    docker network create frontend
    echo "✅ Created 'frontend' network"
else
    echo "ℹ️  'frontend' network already exists"
fi

# Create backend network (for internal services)
if ! docker network ls | grep -q "backend"; then
    docker network create backend
    echo "✅ Created 'backend' network"
else
    echo "ℹ️  'backend' network already exists"
fi

echo "🎉 Network setup complete!"
echo ""
echo "📋 Available networks:"
docker network ls | grep -E "(frontend|backend)"
