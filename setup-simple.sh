#!/bin/bash

# Simple Traefik Setup with Cloudflare DNS
# Based on <PERSON>'s approach

set -e

echo "=== Simple Traefik Setup with Cloudflare DNS ==="

# Check if domain is provided
if [ -z "$1" ]; then
    echo "Usage: $0 <your-domain.com> <cloudflare-api-token>"
    echo "Example: $0 eko-api2.nextai.asia your_cf_token_here"
    exit 1
fi

if [ -z "$2" ]; then
    echo "Error: Cloudflare API token is required"
    echo "Usage: $0 <your-domain.com> <cloudflare-api-token>"
    exit 1
fi

DOMAIN="$1"
CF_TOKEN="$2"

echo "🌐 Setting up Traefik for domain: $DOMAIN"
echo "🔐 Using Cloudflare DNS for SSL certificates"

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p config/conf config/certs logs

# Create ACME file for Cloudflare
echo "🔐 Setting up ACME storage..."
touch config/certs/cloudflare-acme.json
chmod 600 config/certs/cloudflare-acme.json

# Create .env file
echo "⚙️  Creating environment configuration..."
cat > .env << EOF
# Domain Configuration
DOMAIN=$DOMAIN

# Cloudflare API Token for DNS challenge
CF_DNS_API_TOKEN=$CF_TOKEN

# Ports
HTTP_PORT=80
HTTPS_PORT=443
API_PORT=8201
DASHBOARD_PORT=8202
EOF

echo "✅ Environment file created: .env"

# Update configuration files with domain
echo "🔧 Updating configuration files..."
sed -i "s/\${DOMAIN}/$DOMAIN/g" config/traefik.yaml
sed -i "s/\${DOMAIN:-localhost}/$DOMAIN/g" docker-compose.yml

echo "✅ Configuration files updated"

# Create management script
echo "🛠️  Creating management script..."
cat > manage.sh << 'EOF'
#!/bin/bash

case "$1" in
    start)
        echo "🚀 Starting Traefik services..."
        docker-compose up -d
        ;;
    stop)
        echo "🛑 Stopping Traefik services..."
        docker-compose down
        ;;
    restart)
        echo "🔄 Restarting Traefik services..."
        docker-compose down && docker-compose up -d
        ;;
    logs)
        echo "📋 Showing Traefik logs..."
        docker-compose logs -f traefik
        ;;
    status)
        echo "📊 Service Status:"
        docker-compose ps
        echo ""
        echo "🌐 Access Points:"
        source .env
        echo "  - Main App: https://$DOMAIN:8201"
        echo "  - Dashboard: http://$DOMAIN:8202"
        echo "  - HTTPS: https://$DOMAIN"
        ;;
    build)
        echo "🔨 Building application..."
        docker build -t eko-backend:latest .
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|logs|status|build}"
        exit 1
        ;;
esac
EOF

chmod +x manage.sh

echo ""
echo "🎉 Simple Traefik setup completed!"
echo ""
echo "📋 Summary:"
echo "  ✅ Domain: $DOMAIN"
echo "  ✅ SSL: Cloudflare DNS (automatic)"
echo "  ✅ WebSocket: Enabled with sticky sessions"
echo "  ✅ Ports: 80, 443, 8201, 8202"
echo ""
echo "🚀 Next steps:"
echo "  1. Build your application: ./manage.sh build"
echo "  2. Start services: ./manage.sh start"
echo "  3. Check status: ./manage.sh status"
echo "  4. View logs: ./manage.sh logs"
echo ""
echo "🌐 Access URLs:"
echo "  - Main App: https://$DOMAIN:8201"
echo "  - HTTPS: https://$DOMAIN"
echo "  - Dashboard: http://$DOMAIN:8202"
echo ""
echo "⚠️  Make sure your domain DNS is pointing to this server!"
EOF
